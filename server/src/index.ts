import express from "express"
import cors from "cors"

const app = express()
const PORT = process.env.PORT || 3000
const CLIENT_URL = process.env.CLIENT_URL

app.use(express.json())
app.use(
  cors({
    origin: CLIENT_URL,
  })
)

app.get("/api/hello", (_req, res) => {
  res.json({ message: `Hello World! ${Math.random()}` })
})

app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`)
})
