import { useRevalidator } from "react-router"
import type { Route } from "./+types/home"
import { But<PERSON> } from "~/components/ui/button"

export function meta({}: Route.MetaArgs) {
  return [
    { title: "MERN Stack Task" },
    { name: "description", content: "MERN Stack Task" },
  ]
}

export async function clientLoader() {
  const res = await fetch("http://localhost:3000/api/hello")
  const { message } = await res.json()
  return { message }
}

export function HydrateFallback() {
  return <div>Loading...</div>
}

export default function Home({ loaderData }: Route.ComponentProps) {
  const revalidator = useRevalidator()
  return (
    <>
      <h1>{loaderData.message}</h1>
      <Button onClick={() => revalidator.revalidate()}>Revalidate</Button>
    </>
  )
}
