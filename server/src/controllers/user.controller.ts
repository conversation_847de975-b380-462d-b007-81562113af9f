import { Request, Response } from "express"
import User, { IUser } from "../models/User"

export const createUser = async (req: Request, res: Response) => {
  try {
    const { fullName, email, phoneNumber, dateOfBirth, address } = req.body

    // Validate required fields
    if (!fullName || !email || !phoneNumber || !dateOfBirth || !address) {
      return res.status(400).json({
        success: false,
        message: "All fields are required: fullName, email, phoneNumber, dateOfBirth, address",
      })
    }

    // Check if user with email already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() })
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User with this email already exists",
      })
    }

    // Create new user
    const newUser: IUser = new User({
      fullName,
      email,
      phoneNumber,
      dateOfBirth: new Date(dateOfBirth),
      address,
    })

    const savedUser = await newUser.save()

    res.status(201).json({
      success: true,
      message: "User created successfully",
      data: savedUser,
    })
  } catch (error) {
    console.error("Error creating user:", error)
    
    // Handle mongoose validation errors
    if (error instanceof Error && error.name === "ValidationError") {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        error: error.message,
      })
    }

    // Handle duplicate key error (email uniqueness)
    if (error instanceof Error && "code" in error && error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "User with this email already exists",
      })
    }

    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    })
  }
}
