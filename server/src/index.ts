import express from "express"
import cors from "cors"
import { connectDB } from "./config/db"
import userRoutes from "./routes/user.routes"

const app = express()
const PORT = process.env.PORT || 3000
const CLIENT_URL = process.env.CLIENT_URL

// Connect to MongoDB
connectDB()

app.use(express.json())
app.use(
  cors({
    origin: CLIENT_URL,
  })
)

// Routes
app.use("/api/users", userRoutes)

app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`)
})
